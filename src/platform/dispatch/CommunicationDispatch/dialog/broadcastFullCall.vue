<template>
  <bf-dialog
    v-model="visible"
    ref="audioSwitch"
    :title="'广播全呼'"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :modal="false"
    top="30vh"
    class="header-border shadow-md shadow-slate-800 broad-cast-full-call-dialog drag-dialog"
    modal-class="drag-dialog-modal"
    append-to-body
    draggable
    center
  >
    <DispatchFunctionListItem
      class="broadcast-full-call w-[310px] !h-[240px] max-w-[310px] max-h-[240px] m-auto"
      :item="broadcastFullCallOption"
      :isSelected="speakState.current === SpeakState.SPEAKING"
      @click="handleItemClick"
    />
  </bf-dialog>
</template>

<script setup lang="ts">
  import bfDialog from '@/components/bfDialog/main'
  import { watch, ref } from 'vue'
  import { speakState, SpeakState } from '@/utils/speak'
  import { useI18n } from 'vue-i18n'
  import broadcastFullCallSvg from '@/assets/images/dispatch/function_list/broadcast_call.svg'
  import broadcastFullCallSelectedSvg from '@/assets/images/dispatch/function_list/broadcast_call_selected.svg'

  const { t } = useI18n()

  // 接收从openDialog传递的dialogVisible属性
  const props = defineProps<{
    dialogVisible?: boolean
  }>()

  // 定义emit事件，用于更新dialogVisible
  const emit = defineEmits<{
    'update:dialogVisible': [value: boolean]
  }>()

  const broadcastFullCallOption = {
    label: t('dispatch.functionList.broadcastCall'),
    name: 'broadcastCall',
    inactiveIconPath: broadcastFullCallSvg,
    activeIconPath: broadcastFullCallSelectedSvg,
  }

  // 内部状态
  const visible = ref(false)

  // 监听props.dialogVisible的变化
  watch(
    () => props.dialogVisible,
    newVal => {
      if (newVal !== undefined) {
        visible.value = newVal
      }
    },
    { immediate: true }
  )

  // 监听内部visible的变化，同步到父组件
  watch(visible, newVal => {
    emit('update:dialogVisible', newVal)
  })

  const handleItemClick = () => {}
</script>

<style lang="scss">
  .broad-cast-full-call-dialog.el-dialog {
    width: 350px;
    height: 350px;

    .broadcast-full-call {
      span {
        font-size: 17px;
      }
    }
  }
</style>
