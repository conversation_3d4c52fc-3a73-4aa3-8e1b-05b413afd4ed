<template>
  <el-container class="common-bg w-full h-full !flex-col app-layout">
    <!-- 顶部导航 -->
    <BGHead class="flex-none" />

    <!-- 路由页面 -->
    <router-view v-slot="{ Component }">
      <transition name="fade-transform" mode="out-in">
        <!-- 使用 keep-alive 缓存路由组件，确保组件状态保持 -->
        <keep-alive>
          <component :is="Component" />
        </keep-alive>
      </transition>
    </router-view>

    <!-- 设备状态表 -->
    <bf-deviceStatetable
      v-for="item in stateTables"
      :key="'statsTable' + item.name + '_' + item.timestamp"
      :ref="item.name"
      :title="item.title"
      :name="item.name"
      :device="item.device"
    />

    <!-- 紧急报警弹窗 -->
    <bf-emergency-dialog
      v-for="item in bc18AlarmDialogs"
      :key="'bc18' + item.name"
      :ref="'bc18' + item.name"
      :name="item.name"
      :device="item.device"
      :cmdTime="item.cmdTime"
      :dbRid="item.dbRid"
    />
  </el-container>
</template>

<script setup>
  import '@/modules/dataManager'
  import BGHead from '@/layouts/BFHead.vue'
  import { DispatchRouteName } from '@/router'
  import { onBeforeMount } from 'vue'
  window.bfglob.currentPlatform = DispatchRouteName
  import bfprocess from '@/utils/bfprocess.js'
  import { defineAsyncComponent, ref, onMounted } from 'vue'
  import QWebChannel from '@/utils/qWebChannelObj'
  import { initVoipServer, initSpeakInfo } from '@/utils/speak'

  // 异步组件
  const bfEmergencyDialog = defineAsyncComponent(() => import('@/components/secondary/emergencyDialog.vue'))
  const bfDeviceStatetable = defineAsyncComponent(() => import('@/components/secondary/deviceStateTable.vue'))

  // 响应式数据
  const bc18AlarmDialogs = ref([])
  const stateTables = ref([])

  // 方法
  const load_dialog_of_bcxx = (target, ref_str, source) => {
    if (window[source.name]) {
      // 更新对话框
    } else {
      // 创建对话框
      window[source.name] = Date.now()
      target.value.push(source)
    }
  }

  const destroyDialog_of_bcxx = (target, name) => {
    for (const i in target.value) {
      const item = target.value[i]
      if (item.name === name) {
        window[name] = null
        target.value.splice(i, 1)
        break
      }
    }
  }

  // 生命周期
  onMounted(() => {
    // 订阅设备状态表相关事件
    bfglob.on('create_device_status_table', device => {
      let title = device.selfId
      if (device.userName) {
        title += '/' + device.userName
      }
      const tableObj = {
        title: title,
        name: device.rid,
        device: device,
        timestamp: Date.now(), // 添加时间戳，确保每次都能重新创建组件
      }

      // 检查是否已存在相同设备的状态表
      const existingIndex = stateTables.value.findIndex(item => item.name === device.rid)

      if (existingIndex !== -1) {
        // 如果已存在，更新数据并添加新的时间戳来强制重新创建组件
        stateTables.value[existingIndex] = Object.assign(stateTables.value[existingIndex], tableObj)
        stateTables.value = [...stateTables.value]
      } else {
        // 如果不存在，添加新的状态表
        stateTables.value.push(tableObj)
      }
    })

    // 订阅bc18紧急报警弹框消息
    bfglob.on('show_alarm_dialog', (device, cmdTime, dbRid) => {
      const _dialog = {
        name: device.rid,
        device: device,
        cmdTime: cmdTime,
        dbRid: dbRid,
      }
      load_dialog_of_bcxx(bc18AlarmDialogs, 'bc18', _dialog)
    })

    bfglob.on('alarm_destroyDialog', name => {
      destroyDialog_of_bcxx(bc18AlarmDialogs, name)
    })
    // 等待所有必要数据加载完成后再初始化通话信息
    const initSpeakInfoWhenReady = async () => {
      try {
        // 等待用户设置加载完成
        if (!bfglob.userInfo?.setting?.voipSpeakInfo) {
          await new Promise(resolve => {
            const handleUserSettingsUpdate = settings => {
              if (settings.voipSpeakInfo !== undefined) {
                bfglob.off('update_user_settings', handleUserSettingsUpdate)
                resolve()
              }
            }
            bfglob.on('update_user_settings', handleUserSettingsUpdate)
          })
        }

        // 等待组织数据加载完成
        if (bfglob.orgsIsLoaded) {
          await bfglob.orgsIsLoaded
        }

        initSpeakInfo()
      } catch (error) {
        console.error('初始化通话信息失败:', error)
        setTimeout(() => {
          initSpeakInfo()
        }, 1000)
      }
    }
    initVoipServer()
    initSpeakInfoWhenReady()
  })

  bfprocess.loginedAfterFunc()
  QWebChannel.initServer()

  onBeforeMount(() => {
    window.bfglob.treeLoaded = true
  })
</script>

<style scoped lang="scss"></style>
