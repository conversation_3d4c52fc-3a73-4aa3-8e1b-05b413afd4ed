<template>
  <div class="dialog-tree-wrapper">
    <VxeTableTree
      ref="tableTree"
      :withPageHeader="false"
      :filter="true"
      :checkAll="false"
      :checkStrictly="true"
      :filter-source-data="filterSourceData"
      @checkbox-change="onCheckboxChange"
      @cell-dblclick="cellDbClickHandler"
    />
  </div>
</template>

<script lang="ts" setup>
  import { onMounted, useTemplateRef, nextTick, watch } from 'vue'
  import { TreeNodeData, TreeNodeType } from '@/components/common/tableTree'
  import { VxeTableEvents } from 'vxe-table'
  import { checkDmrIdIsGroup } from '@/utils/bfutil'

  const emit = defineEmits(['checkbox-change', 'cell-dblclick'])

  const props = withDefaults(
    defineProps<{
      showOnlyOrg?: boolean
      defaultCheckKeys?: string[] | string
    }>(),
    {
      showOnlyOrg: false,
      defaultCheckKeys: () => [],
    }
  )

  const tableTreeRef = useTemplateRef('tableTree')

  const onCheckboxChange = (row, checked) => {
    console.log('onCheckboxChange', row, checked)
    emit('checkbox-change', row, checked)
  }
  const filterSourceData = (row: TreeNodeData) => {
    if (!props.showOnlyOrg) {
      return true
    }
    if (row.nodeType !== TreeNodeType.Org) {
      row.parentOrgId = ''
      return false
    }
    return true
  }

  // 使用队列方式高效获取节点的所有子孙节点的 dmrId（参考 deepSelectedNodes 的实现）
  const getAllDescendantDmrIds = (nodeRid: string): { groupDmrIds: string[]; individualDmrIds: string[] } => {
    const result = { groupDmrIds: [], individualDmrIds: [] }

    // 获取所有组织数据和设备数据
    const allOrgData = bfglob.gorgData.getAll()
    const allDeviceData = bfglob.gdevices.getAll()

    // 使用队列来遍历，避免深度递归
    const queue = [nodeRid]
    const processedRids = new Set<string>() // 防止循环引用

    while (queue.length > 0) {
      const currentRid = queue.shift()
      if (!currentRid || processedRids.has(currentRid)) {
        continue
      }
      processedRids.add(currentRid)

      // 查找当前节点的子组织
      for (const orgKey in allOrgData) {
        const org = allOrgData[orgKey]
        if (org.parentOrgId === currentRid && !processedRids.has(org.rid)) {
          result.groupDmrIds.push(org.dmrId)
          queue.push(org.rid) // 将子组织加入队列继续处理
        }
      }

      // 查找当前节点的子设备
      for (const deviceKey in allDeviceData) {
        const device = allDeviceData[deviceKey]
        if (device.orgId === currentRid) {
          result.individualDmrIds.push(device.dmrId)
        }
      }
    }

    return result
  }

  const cellDbClickHandler: VxeTableEvents.CellDblclick<TreeNodeData> = ({ row }) => {
    console.log('cellDbClickHandler', row)

    // 获取当前节点的数据
    let currentNodeDmrId = ''
    let currentNodeType = row.nodeType

    if (row.nodeType === TreeNodeType.Org) {
      const orgData = bfglob.gorgData.get(row.rid)
      if (orgData) {
        currentNodeDmrId = orgData.dmrId
      }
    } else {
      const deviceData = bfglob.gdevices.get(row.rid)
      if (deviceData) {
        currentNodeDmrId = deviceData.dmrId
      }
    }

    // 获取所有子孙节点的 dmrId
    const descendantDmrIds = getAllDescendantDmrIds(row.rid)

    // 构建完整的 dmrId 列表（包含当前节点）
    const allGroupDmrIds =
      currentNodeType === TreeNodeType.Org && currentNodeDmrId ? [currentNodeDmrId, ...descendantDmrIds.groupDmrIds] : descendantDmrIds.groupDmrIds
    const allIndividualDmrIds =
      currentNodeType === TreeNodeType.Terminal && currentNodeDmrId
        ? [currentNodeDmrId, ...descendantDmrIds.individualDmrIds]
        : descendantDmrIds.individualDmrIds

    // 发送双击事件，传递节点信息和所有相关的 dmrId
    emit('cell-dblclick', {
      row,
      groupDmrIds: allGroupDmrIds,
      individualDmrIds: allIndividualDmrIds,
    })
  }

  // 标准化 defaultCheckKeys 为数组格式
  const normalizeCheckKeys = (keys: string[] | string): string[] => {
    if (typeof keys === 'string') {
      return keys ? [keys] : []
    }
    return Array.isArray(keys) ? keys : []
  }

  // 设置节点勾选状态
  const setNodeCheckStatus = async (checkKeys: string[]) => {
    await nextTick()

    if (!tableTreeRef.value?.setCheckboxRowByRid) {
      return
    }

    // 首先清除所有节点的勾选状态
    if (tableTreeRef.value?.clearAllCheckboxRow) {
      tableTreeRef.value.clearAllCheckboxRow()
    }

    // 然后设置指定节点为勾选状态
    checkKeys.forEach(dmrId => {
      const isGroup = checkDmrIdIsGroup(dmrId)
      const data = isGroup ? bfglob.gorgData.getDataByIndex(dmrId) : bfglob.gdevices.getDataByIndex(dmrId)
      if (data && data.rid) {
        tableTreeRef.value.setCheckboxRowByRid(data.rid, true)
      }
    })
  }

  const setDefaultCheckedNodes = async () => {
    const checkKeys = normalizeCheckKeys(props.defaultCheckKeys)
    if (checkKeys.length === 0) {
      return
    }
    await setNodeCheckStatus(checkKeys)
  }

  // 监听 defaultCheckKeys 变化
  watch(
    () => props.defaultCheckKeys,
    newKeys => {
      const checkKeys = normalizeCheckKeys(newKeys)
      setNodeCheckStatus(checkKeys)
    },
    { deep: true }
  )

  onMounted(() => {
    setTimeout(() => {
      setDefaultCheckedNodes()
    }, 500) // 增加延迟时间确保树完全加载
  })
</script>

<style lang="scss">
  .dialog-tree-wrapper {
    height: 100%;
    width: 100%;
    font-family: 'AlibabaPuHuiTi2';

    .vxe-table .vxe-table--render-wrapper .vxe-column-content {
      color: #1ac8ed;
    }
  }
</style>
