@use '@/assets/fonts/fonts.css' as *;

.bf-dialog-body-background {
  background: rgba(6, 121, 204, 0.46);
  box-shadow: inset 0 0 9px rgba(14, 190, 255, 0.74);
}

.bf-dialog-body-clip-path {
  --bf-dialog-clip-path: 16px;
  clip-path: polygon(0 0, 100% 0, 100% calc(100% - var(--bf-dialog-clip-path)), calc(100% - var(--bf-dialog-clip-path)) 100%, 0 100%);
}

.el-dialog.bf-dialog {
  @function _px($n) {
    @return #{$n}px;
  }

  // 定义body的背景图切图地方
  $bf-dialog-top-height: 28;
  $bf-dialog-left-border-width: 14;
  $bf-dialog-right-border-width: 28;
  $bf-dialog-bottom-border-width: 30;

  // --- 步骤 1: 容器核心设置 (非常重要) ---
  background: transparent !important; // 使用 !important 确保覆盖 element-ui 的默认白底
  box-shadow: none !important;
  border: none !important; // 必须彻底移除容器自身的边框，防止布局偏移
  position: relative;
  // overflow: visible; // 有时 el-dialog 会有 overflow:hidden，需要取消
  min-height: _px($bf-dialog-top-height + $bf-dialog-bottom-border-width + 120);
  min-width: _px($bf-dialog-left-border-width + $bf-dialog-right-border-width + 360);
  display: flex;
  flex-direction: column;

  // --- 步骤 2: 背景伪元素 (最稳定的方法) ---
  &::before {
    content: '';
    position: absolute;
    top: -24px;
    left: 0;
    right: 22px;
    height: 19px;
    z-index: -1; // 置于内容之下

    border-image-source: url('./images/dialog_top_mask.png');
    // 顺序: top right bottom left
    border-image-slice: 0 21 0 126 fill;
    border-image-width: 0 _px(21) 0 _px(126);
    border-image-repeat: stretch;
  }

  &::after {
    content: '';
    position: absolute;
    inset: -8px;
    z-index: -1; // 置于内容之下

    border-style: solid;
    border-color: transparent;
    border-image-source: url('./images/dialog_body.png');
    // 顺序: top right bottom left
    border-image-slice: $bf-dialog-top-height $bf-dialog-right-border-width $bf-dialog-bottom-border-width $bf-dialog-left-border-width fill;
    border-image-width: _px($bf-dialog-top-height) _px($bf-dialog-right-border-width) _px($bf-dialog-bottom-border-width) _px($bf-dialog-left-border-width);
    border-image-repeat: stretch;
  }

  .el-dialog__header {
    flex: none;
    border-bottom: none;
    display: flex;
    align-items: center;

    .el-dialog__title {
      @extend .alibab-puhui-ti-2;

      flex: 1;
      color: #fff;
      background: linear-gradient(180deg, rgba(81, 224, 255, 0.52) 0%, rgba(81, 224, 255, 0.52) 22.66%, rgba(255, 255, 255, 0.52) 62.72%), #ffffff;
      -webkit-background-clip: text; /* 裁剪背景到文字 */
      -webkit-text-fill-color: transparent; /* 文字透明，让背景透出来 */
      text-shadow: 0px 0px 48px rgba(9, 171, 235, 0.61);
      font-size: 28px;
      line-height: 36px;
      height: 36px;
    }

    .el-dialog__headerbtn {
      top: -40px;
      right: 26px;

      .el-dialog__close {
        width: 52px;
        height: 52px;
      }
    }

    .close-icon {
      background-image: url('./images/close.png');
      background-color: transparent;
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
  }

  .el-dialog__body {
    flex: auto;
    overflow: auto;
  }
}
